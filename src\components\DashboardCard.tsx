
import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface DashboardCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  trend?: 'up' | 'down' | null;
  trendValue?: string;
  className?: string;
  color?: 'blue' | 'green' | 'purple' | 'orange';
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  value,
  icon,
  trend,
  trendValue,
  className,
  color = 'blue'
}) => {
  const getGradient = () => {
    switch (color) {
      case 'green': return 'from-emerald-50 to-white';
      case 'purple': return 'from-violet-50 to-white';
      case 'orange': return 'from-amber-50 to-white';
      case 'blue':
      default: return 'from-blue-50 to-white';
    }
  };

  const getIconColor = () => {
    switch (color) {
      case 'green': return 'bg-emerald-100 text-emerald-600';
      case 'purple': return 'bg-violet-100 text-violet-600';
      case 'orange': return 'bg-amber-100 text-amber-600';
      case 'blue':
      default: return 'bg-blue-100 text-blue-600';
    }
  };

  return (
    <Card className={cn(
      "overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 bg-gradient-to-br",
      getGradient(),
      className
    )}>
      <CardHeader className="p-5 pb-0 flex items-center justify-between">
        <h3 className="text-gray-700 text-sm font-semibold tracking-wide">{title}</h3>
        {icon && (
          <span className={cn(
            "p-2 rounded-full",
            getIconColor()
          )}>
            {icon}
          </span>
        )}
      </CardHeader>
      <CardContent className="p-5 pt-3">
        <div className="flex items-end">
          <p className="text-2xl font-bold text-gray-800">{value}</p>
          {trend && (
            <span className={cn(
              "ml-2 text-sm font-medium flex items-center rounded-full px-2 py-0.5",
              trend === 'up'
                ? 'text-emerald-700 bg-emerald-50'
                : 'text-red-700 bg-red-50'
            )}>
              {trend === 'up' ? '↑' : '↓'} {trendValue}
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default DashboardCard;
