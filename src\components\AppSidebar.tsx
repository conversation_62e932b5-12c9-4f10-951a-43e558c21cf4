
import React from 'react';
import { NavLink } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import {
  Home,
  Building,
  FileText,
  BarChart,
  Settings,
  LogOut,
  CreditCard
} from 'lucide-react';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";

const AppSidebar = () => {
  const { logout } = useAuth();

  const navigation = [
    { name: 'Dashboard', path: '/dashboard', icon: Home },
    { name: 'Businesses', path: '/businesses', icon: Building },
    { name: 'Transactions', path: '/transactions', icon: FileText },
    { name: 'Analytics', path: '/analytics', icon: BarChart },
    { name: 'Settings', path: '/settings', icon: Settings },
  ];

  return (
    <Sidebar className="bg-gradient-to-b from-primary via-primary to-primary/90 shadow-xl border-r border-primary/20">
      <SidebarHeader className="py-8 px-6 bg-gradient-to-r from-primary/90 to-secondary/20 backdrop-blur-sm">
        <div className="flex items-center justify-center space-x-2">
          <div className="p-2 bg-white/10 rounded-lg backdrop-blur-sm">
            <CreditCard className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-white font-bold text-2xl tracking-tight">InfoPay</h1>
            <p className="text-white/70 text-xs font-medium">Payment Gateway</p>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-3 py-4">
        <SidebarGroup>
          <SidebarMenu className="space-y-2">
            {navigation.map((item) => (
              <SidebarMenuItem key={item.name}>
                <SidebarMenuButton asChild>
                  <NavLink
                    to={item.path}
                    className={({ isActive }) => cn(
                      "flex items-center gap-3 px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 group relative overflow-hidden",
                      isActive
                        ? "bg-white/15 text-white shadow-lg backdrop-blur-sm border border-white/20"
                        : "text-white/80 hover:bg-white/10 hover:text-white hover:shadow-md hover:backdrop-blur-sm"
                    )}
                  >
                    <div className={cn(
                      "p-1.5 rounded-lg transition-all duration-200",
                      "group-hover:bg-white/10"
                    )}>
                      <item.icon className="h-4 w-4" />
                    </div>
                    <span className="font-medium">{item.name}</span>
                    {/* Active indicator */}
                    <div className="absolute right-2 w-1 h-6 bg-white/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t border-white/10 p-4 bg-primary/50 backdrop-blur-sm">
        <button
          onClick={logout}
          className="w-full flex items-center gap-3 px-4 py-3 rounded-xl text-sm font-medium text-white/80 hover:bg-white/10 hover:text-white transition-all duration-200 group"
        >
          <div className="p-1.5 rounded-lg group-hover:bg-white/10 transition-all duration-200">
            <LogOut className="h-4 w-4" />
          </div>
          <span className="font-medium">Logout</span>
        </button>
      </SidebarFooter>
    </Sidebar>
  );
};

export default AppSidebar;
