import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle2 } from 'lucide-react';

const About = () => {
  return (
    <div className="container mx-auto px-4 py-12">
      {/* Hero Section */}
      <div className="max-w-4xl mx-auto mb-16 text-center">
        <h1 className="text-4xl font-bold mb-4">About BulkSend Cloud</h1>
        <p className="text-xl text-gray-600">
          We're building the future of digital communication and payments in Malawi.
        </p>
      </div>

      {/* Our Story */}
      <div className="max-w-4xl mx-auto mb-16">
        <h2 className="text-3xl font-bold mb-6">Our Story</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          <div>
            <p className="text-gray-700 mb-4">
              Founded in 2020, BulkSend Cloud was created to solve the communication and payment challenges faced by businesses in Malawi.
              We recognized that many businesses struggled with reliable customer communication and payment processing, especially for mobile-based
              services which are dominant in the country.
            </p>
            <p className="text-gray-700 mb-4">
              Our team of communication technology experts and software engineers came together to build solutions that
              would make customer engagement simple, reliable, and affordable for businesses of all sizes through multiple channels including
              SMS, WhatsApp, USSD, and payments.
            </p>
            <p className="text-gray-700">
              Today, BulkSend Cloud is trusted by hundreds of businesses across Malawi, processing millions of messages and
              transactions every month. We continue to innovate and expand our services to meet the evolving needs
              of our customers.
            </p>
          </div>
          <div className="rounded-lg overflow-hidden shadow-lg">
            <img
              src="https://placehold.co/600x400/1A365D/FFFFFF/png?text=BulkSend+Cloud+Team"
              alt="BulkSend Cloud Team"
              className="w-full h-auto"
            />
          </div>
        </div>
      </div>

      {/* Our Mission */}
      <div className="bg-gray-100 py-16 -mx-4 px-4 mb-16">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6">Our Mission</h2>
          <p className="text-xl text-gray-700 mb-8">
            To empower businesses in Malawi with reliable, secure, and accessible communication and payment solutions that drive
            customer engagement, economic growth, and digital inclusion.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="pt-6">
                <div className="rounded-full bg-primary/10 p-3 w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <CheckCircle2 className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2 text-center">Reliability</h3>
                <p className="text-gray-600 text-center">
                  Building systems that businesses can depend on 24/7.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="rounded-full bg-primary/10 p-3 w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <CheckCircle2 className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2 text-center">Innovation</h3>
                <p className="text-gray-600 text-center">
                  Continuously improving our technology to meet evolving needs.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="rounded-full bg-primary/10 p-3 w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <CheckCircle2 className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2 text-center">Accessibility</h3>
                <p className="text-gray-600 text-center">
                  Making digital payments accessible to businesses of all sizes.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Our Team */}
      <div className="max-w-4xl mx-auto mb-16">
        <h2 className="text-3xl font-bold mb-6">Our Leadership Team</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="rounded-full overflow-hidden w-32 h-32 mx-auto mb-4">
              <img
                src="https://placehold.co/300x300/1A365D/FFFFFF/png?text=CEO"
                alt="CEO"
                className="w-full h-full object-cover"
              />
            </div>
            <h3 className="text-xl font-bold">John Banda</h3>
            <p className="text-gray-600">Chief Executive Officer</p>
          </div>
          <div className="text-center">
            <div className="rounded-full overflow-hidden w-32 h-32 mx-auto mb-4">
              <img
                src="https://placehold.co/300x300/1A365D/FFFFFF/png?text=CTO"
                alt="CTO"
                className="w-full h-full object-cover"
              />
            </div>
            <h3 className="text-xl font-bold">Mary Phiri</h3>
            <p className="text-gray-600">Chief Technology Officer</p>
          </div>
          <div className="text-center">
            <div className="rounded-full overflow-hidden w-32 h-32 mx-auto mb-4">
              <img
                src="https://placehold.co/300x300/1A365D/FFFFFF/png?text=COO"
                alt="COO"
                className="w-full h-full object-cover"
              />
            </div>
            <h3 className="text-xl font-bold">David Mwanza</h3>
            <p className="text-gray-600">Chief Operations Officer</p>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="max-w-4xl mx-auto text-center">
        <h2 className="text-3xl font-bold mb-4">Join the BulkSend Cloud Community</h2>
        <p className="text-xl text-gray-600 mb-8">
          Be part of the digital communication revolution in Malawi.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild size="lg">
            <Link to="/register">Create an Account</Link>
          </Button>
          <Button asChild variant="outline" size="lg">
            <Link to="/contact">Contact Us</Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default About;
