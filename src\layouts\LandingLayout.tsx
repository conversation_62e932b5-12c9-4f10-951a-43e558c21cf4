import React, { useState } from 'react';
import { Link, Outlet } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { cn } from '@/lib/utils';
import { Menu, X, ChevronDown } from 'lucide-react';
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const LandingLayout = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <header className="border-b bg-white sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Link to="/" className="text-2xl font-bold text-primary">BulkSend Cloud</Link>
          </div>

          {/* Desktop Navigation */}
          <NavigationMenu className="hidden md:flex">
            <NavigationMenuList>
              <NavigationMenuItem>
                <Link to="/">
                  <NavigationMenuLink className={navigationMenuTriggerStyle()}>
                    Home
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <NavigationMenuTrigger>Products</NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                    <li>
                      <NavigationMenuLink asChild>
                        <Link to="/products/bulk-sms" className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                          <div className="text-sm font-medium leading-none">Bulk SMS Gateway</div>
                          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                            Send powerful bulk SMS campaigns to your customers
                          </p>
                        </Link>
                      </NavigationMenuLink>
                    </li>
                    <li>
                      <NavigationMenuLink asChild>
                        <Link to="/products/whatsapp" className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                          <div className="text-sm font-medium leading-none">WhatsApp Gateway</div>
                          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                            Connect with your customers via WhatsApp
                          </p>
                        </Link>
                      </NavigationMenuLink>
                    </li>
                    <li>
                      <NavigationMenuLink asChild>
                        <Link to="/products/ussd" className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                          <div className="text-sm font-medium leading-none">USSD Gateway</div>
                          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                            Create interactive USSD services
                          </p>
                        </Link>
                      </NavigationMenuLink>
                    </li>
                    <li>
                      <NavigationMenuLink asChild>
                        <Link to="/products/payment" className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                          <div className="text-sm font-medium leading-none">Payment Gateway</div>
                          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                            Accept payments easily with our reliable gateway
                          </p>
                        </Link>
                      </NavigationMenuLink>
                    </li>
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <Link to="/api-docs">
                  <NavigationMenuLink className={navigationMenuTriggerStyle()}>
                    API Docs
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link to="/about">
                  <NavigationMenuLink className={navigationMenuTriggerStyle()}>
                    About
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link to="/contact">
                  <NavigationMenuLink className={navigationMenuTriggerStyle()}>
                    Contact
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>

          {/* Desktop Buttons */}
          <div className="hidden md:flex items-center gap-4">
            <Button asChild variant="outline">
              <Link to="/dashboard-login">Login</Link>
            </Button>
            <Button asChild>
              <Link to="/register">Sign Up</Link>
            </Button>
          </div>

          {/* Mobile Menu */}
          <Sheet>
            <SheetTrigger asChild className="md:hidden">
              <Button variant="ghost" size="icon">
                <Menu className="h-6 w-6" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <div className="flex flex-col gap-6 py-6">
                <Link to="/" className="text-2xl font-bold text-primary">BulkSend Cloud</Link>

                <nav className="flex flex-col gap-4">
                  <Link to="/" className="text-lg font-medium">Home</Link>

                  <Accordion type="single" collapsible>
                    <AccordionItem value="products">
                      <AccordionTrigger className="text-lg font-medium">Products</AccordionTrigger>
                      <AccordionContent>
                        <div className="flex flex-col gap-3 pl-4">
                          <Link to="/products/bulk-sms" className="text-base">Bulk SMS Gateway</Link>
                          <Link to="/products/whatsapp" className="text-base">WhatsApp Gateway</Link>
                          <Link to="/products/ussd" className="text-base">USSD Gateway</Link>
                          <Link to="/products/payment" className="text-base">Payment Gateway</Link>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>

                  <Link to="/api-docs" className="text-lg font-medium">API Docs</Link>
                  <Link to="/about" className="text-lg font-medium">About</Link>
                  <Link to="/contact" className="text-lg font-medium">Contact</Link>
                </nav>

                <div className="flex flex-col gap-3 mt-4">
                  <Button asChild variant="outline">
                    <Link to="/dashboard-login">Login</Link>
                  </Button>
                  <Button asChild>
                    <Link to="/register">Sign Up</Link>
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        <Outlet />
      </main>

      {/* Footer */}
      <footer className="bg-gray-100 border-t">
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">BulkSend Cloud</h3>
              <p className="text-gray-600">
                Communication channel for all your ideas. Connect with your customers through multiple channels.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Products</h3>
              <ul className="space-y-2">
                <li><Link to="/products/bulk-sms" className="text-gray-600 hover:text-primary">Bulk SMS Gateway</Link></li>
                <li><Link to="/products/whatsapp" className="text-gray-600 hover:text-primary">WhatsApp Gateway</Link></li>
                <li><Link to="/products/ussd" className="text-gray-600 hover:text-primary">USSD Gateway</Link></li>
                <li><Link to="/products/payment" className="text-gray-600 hover:text-primary">Payment Gateway</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Resources</h3>
              <ul className="space-y-2">
                <li><Link to="/api-docs" className="text-gray-600 hover:text-primary">API Documentation</Link></li>
                <li><Link to="/" className="text-gray-600 hover:text-primary">Tutorials</Link></li>
                <li><Link to="/" className="text-gray-600 hover:text-primary">FAQs</Link></li>
                <li><Link to="/" className="text-gray-600 hover:text-primary">Developer Hub</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Company</h3>
              <ul className="space-y-2">
                <li><Link to="/about" className="text-gray-600 hover:text-primary">About Us</Link></li>
                <li><Link to="/contact" className="text-gray-600 hover:text-primary">Contact</Link></li>
                <li><Link to="/" className="text-gray-600 hover:text-primary">Privacy Policy</Link></li>
                <li><Link to="/" className="text-gray-600 hover:text-primary">Terms of Service</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t mt-8 pt-8 text-center text-gray-600">
            <p>&copy; {new Date().getFullYear()} BulkSend Cloud. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingLayout;
