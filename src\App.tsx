
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";

// Layouts
import DashboardLayout from "./layouts/DashboardLayout";
import AuthLayout from "./layouts/AuthLayout";
import LandingLayout from "./layouts/LandingLayout";

// Pages
import Login from "./pages/Login";
import Register from "./pages/Register";
import Dashboard from "./pages/Dashboard";
import Businesses from "./pages/Businesses";
import Transactions from "./pages/Transactions";
import Analytics from "./pages/Analytics";
import Settings from "./pages/Settings";
import NotFound from "./pages/NotFound";

// Landing Pages
import Home from "./pages/Home";
import ApiDocs from "./pages/ApiDocs";
import About from "./pages/About";
import Contact from "./pages/Contact";
import DashboardLogin from "./pages/DashboardLogin";

// Product Pages
import BulkSms from "./pages/products/BulkSms";
import WhatsApp from "./pages/products/WhatsApp";
import Ussd from "./pages/products/Ussd";
import Payment from "./pages/products/Payment";

const queryClient = new QueryClient();

const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      {/* Remove TooltipProvider as it's causing the React hooks error */}
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <Routes>
            {/* Landing Pages */}
            <Route element={<LandingLayout />}>
              <Route path="/" element={<Home />} />
              <Route path="/api-docs" element={<ApiDocs />} />
              <Route path="/about" element={<About />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/dashboard-login" element={<DashboardLogin />} />

              {/* Product Pages */}
              <Route path="/products/bulk-sms" element={<BulkSms />} />
              <Route path="/products/whatsapp" element={<WhatsApp />} />
              <Route path="/products/ussd" element={<Ussd />} />
              <Route path="/products/payment" element={<Payment />} />
            </Route>

            {/* Auth Routes */}
            <Route element={<AuthLayout />}>
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              {/* Add forgot-password and reset-password routes here if needed */}
            </Route>

            {/* Dashboard Routes */}
            <Route element={<DashboardLayout />}>
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/businesses" element={<Businesses />} />
              <Route path="/transactions" element={<Transactions />} />
              <Route path="/analytics" element={<Analytics />} />
              <Route path="/settings" element={<Settings />} />
            </Route>

            {/* 404 Route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

export default App;
