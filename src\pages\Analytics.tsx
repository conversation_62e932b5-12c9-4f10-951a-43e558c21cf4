
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { format, subDays } from 'date-fns';
import {
  <PERSON>Chart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
} from 'recharts';
import DashboardCard from '@/components/DashboardCard';
import { FileText } from 'lucide-react';

const Analytics = () => {
  const [dateRange, setDateRange] = useState('7days');
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data for the charts
  const revenueData = [
    { date: format(subDays(new Date(), 6), 'MMM dd'), amount: 1250.50 },
    { date: format(subDays(new Date(), 5), 'MMM dd'), amount: 2340.75 },
    { date: format(subDays(new Date(), 4), 'MMM dd'), amount: 1890.25 },
    { date: format(subDays(new Date(), 3), 'MMM dd'), amount: 3120.40 },
    { date: format(subDays(new Date(), 2), 'MMM dd'), amount: 2760.30 },
    { date: format(subDays(new Date(), 1), 'MMM dd'), amount: 1970.60 },
    { date: format(new Date(), 'MMM dd'), amount: 2420.00 },
  ];

  const transactionData = [
    { date: format(subDays(new Date(), 6), 'MMM dd'), count: 25 },
    { date: format(subDays(new Date(), 5), 'MMM dd'), count: 42 },
    { date: format(subDays(new Date(), 4), 'MMM dd'), count: 35 },
    { date: format(subDays(new Date(), 3), 'MMM dd'), count: 51 },
    { date: format(subDays(new Date(), 2), 'MMM dd'), count: 49 },
    { date: format(subDays(new Date(), 1), 'MMM dd'), count: 38 },
    { date: format(new Date(), 'MMM dd'), count: 45 },
  ];

  const paymentMethodData = [
    { name: 'Wallet', value: 45, color: '#1A365D' },
    { name: 'Airtel Money', value: 25, color: '#0D9488' },
    { name: 'M-Pesa', value: 20, color: '#F7C948' },
    { name: 'TNM Mpamba', value: 10, color: '#3B82F6' },
  ];

  const customerData = [
    { date: format(subDays(new Date(), 6), 'MMM dd'), newCustomers: 5, returningCustomers: 8 },
    { date: format(subDays(new Date(), 5), 'MMM dd'), newCustomers: 7, returningCustomers: 12 },
    { date: format(subDays(new Date(), 4), 'MMM dd'), newCustomers: 4, returningCustomers: 10 },
    { date: format(subDays(new Date(), 3), 'MMM dd'), newCustomers: 6, returningCustomers: 15 },
    { date: format(subDays(new Date(), 2), 'MMM dd'), newCustomers: 3, returningCustomers: 14 },
    { date: format(subDays(new Date(), 1), 'MMM dd'), newCustomers: 5, returningCustomers: 11 },
    { date: format(new Date(), 'MMM dd'), newCustomers: 8, returningCustomers: 13 },
  ];

  const handleDateRangeChange = (value: string) => {
    setDateRange(value);
    // In a real application, we would fetch new data based on the selected date range
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Analytics</h1>
          <p className="text-muted-foreground">Track your payment performance and customer insights.</p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={dateRange} onValueChange={handleDateRangeChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
              <SelectItem value="year">This year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <FileText className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3 mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="customers">Customers</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          {/* Key metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
            <DashboardCard
              title="Total Revenue"
              value="K15,750.80"
              trend="up"
              trendValue="12.5%"
            />
            <DashboardCard
              title="Transactions"
              value="245"
              trend="up"
              trendValue="8.2%"
            />
            <DashboardCard
              title="Success Rate"
              value="90.2%"
              trend="up"
              trendValue="2.1%"
            />
            <DashboardCard
              title="Average Value"
              value="K64.29"
              trend="down"
              trendValue="1.3%"
            />
          </div>

          {/* Revenue Chart */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Revenue</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={revenueData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip
                      formatter={(value: number) => [`K${value.toFixed(2)}`, 'Revenue']}
                      labelFormatter={(label) => `Date: ${label}`}
                    />
                    <Bar dataKey="amount" fill="#0D9488" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Payment Methods Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Methods</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={paymentMethodData}
                      cx="50%"
                      cy="50%"
                      innerRadius={80}
                      outerRadius={120}
                      fill="#8884d8"
                      paddingAngle={2}
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {paymentMethodData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Legend verticalAlign="bottom" height={36} />
                    <Tooltip formatter={(value) => [`${value}%`, 'Percentage']} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions">
          {/* Transaction metrics */}
          <div className="grid gap-4 md:grid-cols-3 mb-6">
            <DashboardCard
              title="Total Transactions"
              value="245"
            />
            <DashboardCard
              title="Successful Transactions"
              value="221"
            />
            <DashboardCard
              title="Failed Transactions"
              value="24"
            />
          </div>

          {/* Transaction Chart */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Transaction Volume</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={transactionData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip
                      formatter={(value: number) => [`${value}`, 'Transactions']}
                      labelFormatter={(label) => `Date: ${label}`}
                    />
                    <Line type="monotone" dataKey="count" stroke="#1A365D" activeDot={{ r: 8 }} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Transaction Status Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Transaction Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80 flex items-center justify-center">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Success', value: 90.2, color: '#10B981' },
                        { name: 'Failed', value: 4.9, color: '#EF4444' },
                        { name: 'Pending', value: 4.9, color: '#F59E0B' },
                      ]}
                      cx="50%"
                      cy="50%"
                      innerRadius={80}
                      outerRadius={120}
                      fill="#8884d8"
                      paddingAngle={2}
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {[
                        { name: 'Success', value: 90.2, color: '#10B981' },
                        { name: 'Failed', value: 4.9, color: '#EF4444' },
                        { name: 'Pending', value: 4.9, color: '#F59E0B' },
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Legend verticalAlign="bottom" height={36} />
                    <Tooltip formatter={(value) => [`${value}%`, 'Percentage']} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customers">
          {/* Customer metrics */}
          <div className="grid gap-4 md:grid-cols-3 mb-6">
            <DashboardCard
              title="Total Customers"
              value="152"
            />
            <DashboardCard
              title="New Customers"
              value="38"
              trend="up"
              trendValue="15.2%"
            />
            <DashboardCard
              title="Returning Customers"
              value="114"
              trend="up"
              trendValue="5.5%"
            />
          </div>

          {/* Customer Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={customerData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="newCustomers" name="New Customers" fill="#0D9488" radius={[4, 4, 0, 0]} />
                    <Bar dataKey="returningCustomers" name="Returning Customers" fill="#1A365D" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Analytics;
