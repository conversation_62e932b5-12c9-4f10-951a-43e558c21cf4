
import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Business, businessService } from '@/services/api';
import { Building, Plus } from 'lucide-react';
import { toast } from 'sonner';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';

const Businesses = () => {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });
  const [showApiKey, setShowApiKey] = useState(false);

  useEffect(() => {
    fetchBusinesses();
  }, []);

  const fetchBusinesses = async () => {
    try {
      setLoading(true);
      console.log('Fetching businesses...');
      const response = await businessService.getBusinesses();
      console.log('Businesses response:', response);
      setBusinesses(response.data.data);
    } catch (error) {
      console.error('Error fetching businesses:', error);
      toast.error('Failed to load businesses');

      // Fallback to mock data
      console.log('Using mock data as fallback');
      setBusinesses([
        {
          id: '1',
          name: 'E-commerce Store',
          description: 'Online electronics retail store',
          api_key: 'a1b2c3d4e5f6g7h8i9j0',
          api_secret: 'k1l2m3n4o5p6q7r8s9t0',
          created_at: '2025-04-10T14:30:00Z',
          updated_at: '2025-05-15T09:45:00Z',
        },
        {
          id: '2',
          name: 'Service Agency',
          description: 'Professional services platform',
          api_key: 'b2c3d4e5f6g7h8i9j0k1',
          api_secret: 'l2m3n4o5p6q7r8s9t0u1',
          created_at: '2025-05-01T11:20:00Z',
          updated_at: '2025-05-17T16:15:00Z',
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBusiness = async () => {
    try {
      const response = await businessService.createBusiness(formData);
      setBusinesses([...businesses, response.data.data]);
      setIsCreateDialogOpen(false);
      setFormData({ name: '', description: '' });
      toast.success('Business created successfully');
    } catch (error) {
      console.error('Error creating business:', error);
      toast.error('Failed to create business');
    }
  };

  const handleRegenerateKey = async (businessId: string) => {
    try {
      const response = await businessService.regenerateKey(businessId);
      if (selectedBusiness) {
        const updatedBusiness = {
          ...selectedBusiness,
          api_key: response.data.data.api_key,
          api_secret: response.data.data.api_secret
        };
        setSelectedBusiness(updatedBusiness);
        setBusinesses(businesses.map(b => b.id === businessId ? updatedBusiness : b));
        toast.success('API key regenerated successfully');
      }
    } catch (error) {
      console.error('Error regenerating key:', error);
      toast.error('Failed to regenerate API key');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const openViewDialog = (business: Business) => {
    setSelectedBusiness(business);
    setIsViewDialogOpen(true);
    setShowApiKey(false);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <p>Loading businesses...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Businesses</h1>
          <p className="text-muted-foreground">Manage your business profiles and API keys.</p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Business
        </Button>
      </div>

      {businesses.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Building className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No businesses yet</h3>
            <p className="text-muted-foreground mb-4">Create your first business to get started</p>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Business
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {businesses.map((business) => (
            <Card key={business.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  {business.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                  {business.description || 'No description provided'}
                </p>
                <div className="text-xs text-muted-foreground mb-4">
                  Created on {new Date(business.created_at).toLocaleDateString()}
                </div>
                <Button onClick={() => openViewDialog(business)} variant="outline" className="w-full">
                  View Details
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Create Business Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Business</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Business Name</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter business name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter business description"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateBusiness} disabled={!formData.name.trim()}>
              Create Business
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Business Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Business Details</DialogTitle>
          </DialogHeader>
          {selectedBusiness && (
            <div className="space-y-4">
              <div className="space-y-1">
                <Label>Business Name</Label>
                <p className="text-sm font-medium">{selectedBusiness.name}</p>
              </div>

              {selectedBusiness.description && (
                <div className="space-y-1">
                  <Label>Description</Label>
                  <p className="text-sm">{selectedBusiness.description}</p>
                </div>
              )}

              <Separator />

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>API Key</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowApiKey(!showApiKey)}
                    className="text-xs"
                  >
                    {showApiKey ? 'Hide' : 'Show'}
                  </Button>
                </div>
                <div className="bg-gray-50 p-2 rounded border font-mono text-sm break-all">
                  {showApiKey ? selectedBusiness.api_key : '••••••••••••••••••••'}
                </div>
              </div>

              <div className="space-y-1">
                <Label>Created On</Label>
                <p className="text-sm">{new Date(selectedBusiness.created_at).toLocaleString()}</p>
              </div>

              <div className="space-y-1">
                <Label>Last Updated</Label>
                <p className="text-sm">{new Date(selectedBusiness.updated_at).toLocaleString()}</p>
              </div>

              <Button
                variant="outline"
                className="w-full"
                onClick={() => handleRegenerateKey(selectedBusiness.id)}
              >
                Regenerate API Key
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Businesses;
