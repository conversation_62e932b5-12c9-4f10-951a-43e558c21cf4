
import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Business, businessService, paymentService } from '@/services/api';
import { Building, Plus, TestTube, Loader2, CheckCircle, XCircle, Clock } from 'lucide-react';
import { toast } from 'sonner';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const Businesses = () => {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isTestApiDialogOpen, setIsTestApiDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });
  const [showApiKey, setShowApiKey] = useState(false);

  // API Testing state
  const [testFormData, setTestFormData] = useState({
    amount: '',
    phone: '',
    payment_method: '',
    description: '',
    reference: '',
    callback_url: ''
  });
  const [isTestingApi, setIsTestingApi] = useState(false);
  const [testResult, setTestResult] = useState<{
    status: 'success' | 'error' | 'pending' | null;
    data?: any;
    message?: string;
  }>({ status: null });

  useEffect(() => {
    fetchBusinesses();
  }, []);

  const fetchBusinesses = async () => {
    try {
      setLoading(true);
      console.log('Fetching businesses...');
      const response = await businessService.getBusinesses();
      console.log('Businesses response:', response);
      setBusinesses(response.data.data);
    } catch (error) {
      console.error('Error fetching businesses:', error);
      toast.error('Failed to load businesses');

      // Fallback to mock data
      console.log('Using mock data as fallback');
      setBusinesses([
        {
          id: '1',
          name: 'E-commerce Store',
          description: 'Online electronics retail store',
          api_key: 'a1b2c3d4e5f6g7h8i9j0',
          api_secret: 'k1l2m3n4o5p6q7r8s9t0',
          created_at: '2025-04-10T14:30:00Z',
          updated_at: '2025-05-15T09:45:00Z',
        },
        {
          id: '2',
          name: 'Service Agency',
          description: 'Professional services platform',
          api_key: 'b2c3d4e5f6g7h8i9j0k1',
          api_secret: 'l2m3n4o5p6q7r8s9t0u1',
          created_at: '2025-05-01T11:20:00Z',
          updated_at: '2025-05-17T16:15:00Z',
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBusiness = async () => {
    try {
      const response = await businessService.createBusiness(formData);
      setBusinesses([...businesses, response.data.data]);
      setIsCreateDialogOpen(false);
      setFormData({ name: '', description: '' });
      toast.success('Business created successfully');
    } catch (error) {
      console.error('Error creating business:', error);
      toast.error('Failed to create business');
    }
  };

  const handleRegenerateKey = async (businessId: string) => {
    try {
      const response = await businessService.regenerateKey(businessId);
      if (selectedBusiness) {
        const updatedBusiness = {
          ...selectedBusiness,
          api_key: response.data.data.api_key,
          api_secret: response.data.data.api_secret
        };
        setSelectedBusiness(updatedBusiness);
        setBusinesses(businesses.map(b => b.id === businessId ? updatedBusiness : b));
        toast.success('API key regenerated successfully');
      }
    } catch (error) {
      console.error('Error regenerating key:', error);
      toast.error('Failed to regenerate API key');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const openViewDialog = (business: Business) => {
    setSelectedBusiness(business);
    setIsViewDialogOpen(true);
    setShowApiKey(false);
  };

  const openTestApiDialog = (business: Business) => {
    setSelectedBusiness(business);
    setIsTestApiDialogOpen(true);
    setTestResult({ status: null });
    setTestFormData({
      amount: '',
      phone: '',
      payment_method: '',
      description: '',
      reference: '',
      callback_url: ''
    });
  };

  const handleTestFormChange = (field: string, value: string) => {
    setTestFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleTestApi = async () => {
    if (!selectedBusiness || !testFormData.amount || !testFormData.phone || !testFormData.payment_method) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsTestingApi(true);
    setTestResult({ status: 'pending', message: 'Processing payment...' });

    try {
      const paymentData = {
        amount: parseFloat(testFormData.amount),
        phone: testFormData.phone,
        payment_method: testFormData.payment_method,
        description: testFormData.description || 'API Test Payment',
        reference: testFormData.reference || `test-${Date.now()}`,
        callback_url: testFormData.callback_url || undefined
      };

      const response = await paymentService.processPayment(paymentData, selectedBusiness.api_key);

      setTestResult({
        status: 'success',
        data: response.data,
        message: 'Payment initiated successfully!'
      });

      toast.success('Payment test completed successfully');
    } catch (error: any) {
      console.error('API test error:', error);
      setTestResult({
        status: 'error',
        message: error.response?.data?.message || 'Payment test failed',
        data: error.response?.data
      });
      toast.error('Payment test failed');
    } finally {
      setIsTestingApi(false);
    }
  };

  const resetTestForm = () => {
    setTestFormData({
      amount: '',
      phone: '',
      payment_method: '',
      description: '',
      reference: '',
      callback_url: ''
    });
    setTestResult({ status: null });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <p>Loading businesses...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Businesses</h1>
          <p className="text-muted-foreground">Manage your business profiles and API keys.</p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Business
        </Button>
      </div>

      {businesses.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Building className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No businesses yet</h3>
            <p className="text-muted-foreground mb-4">Create your first business to get started</p>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Business
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {businesses.map((business) => (
            <Card key={business.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  {business.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                  {business.description || 'No description provided'}
                </p>
                <div className="text-xs text-muted-foreground mb-4">
                  Created on {new Date(business.created_at).toLocaleDateString()}
                </div>
                <div className="flex gap-2">
                  <Button onClick={() => openViewDialog(business)} variant="outline" className="flex-1">
                    View Details
                  </Button>
                  <Button onClick={() => openTestApiDialog(business)} variant="secondary" className="flex-1">
                    <TestTube className="mr-2 h-4 w-4" />
                    Test API
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Create Business Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Business</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Business Name</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter business name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter business description"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateBusiness} disabled={!formData.name.trim()}>
              Create Business
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Business Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Business Details</DialogTitle>
          </DialogHeader>
          {selectedBusiness && (
            <div className="space-y-4">
              <div className="space-y-1">
                <Label>Business Name</Label>
                <p className="text-sm font-medium">{selectedBusiness.name}</p>
              </div>

              {selectedBusiness.description && (
                <div className="space-y-1">
                  <Label>Description</Label>
                  <p className="text-sm">{selectedBusiness.description}</p>
                </div>
              )}

              <Separator />

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>API Key</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowApiKey(!showApiKey)}
                    className="text-xs"
                  >
                    {showApiKey ? 'Hide' : 'Show'}
                  </Button>
                </div>
                <div className="bg-gray-50 p-2 rounded border font-mono text-sm break-all">
                  {showApiKey ? selectedBusiness.api_key : '••••••••••••••••••••'}
                </div>
              </div>

              <div className="space-y-1">
                <Label>Created On</Label>
                <p className="text-sm">{new Date(selectedBusiness.created_at).toLocaleString()}</p>
              </div>

              <div className="space-y-1">
                <Label>Last Updated</Label>
                <p className="text-sm">{new Date(selectedBusiness.updated_at).toLocaleString()}</p>
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => handleRegenerateKey(selectedBusiness.id)}
                >
                  Regenerate API Key
                </Button>
                <Button
                  variant="secondary"
                  className="flex-1"
                  onClick={() => {
                    setIsViewDialogOpen(false);
                    openTestApiDialog(selectedBusiness);
                  }}
                >
                  <TestTube className="mr-2 h-4 w-4" />
                  Test API
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* API Testing Dialog */}
      <Dialog open={isTestApiDialogOpen} onOpenChange={setIsTestApiDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <TestTube className="h-5 w-5" />
              Test Payment API
            </DialogTitle>
          </DialogHeader>
          {selectedBusiness && (
            <div className="space-y-4">
              <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                <p className="text-sm text-blue-800">
                  <strong>Testing API for:</strong> {selectedBusiness.name}
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  This will make a real API call to test the payment gateway
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="test-amount">Amount *</Label>
                  <Input
                    id="test-amount"
                    type="number"
                    placeholder="100.00"
                    value={testFormData.amount}
                    onChange={(e) => handleTestFormChange('amount', e.target.value)}
                    min="1"
                    step="0.01"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="test-phone">Phone Number *</Label>
                  <Input
                    id="test-phone"
                    placeholder="260977123456"
                    value={testFormData.phone}
                    onChange={(e) => handleTestFormChange('phone', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="test-payment-method">Payment Method *</Label>
                <Select
                  value={testFormData.payment_method}
                  onValueChange={(value) => handleTestFormChange('payment_method', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="wallet">Mobile Wallet</SelectItem>
                    <SelectItem value="mtn_momo">MTN Mobile Money</SelectItem>
                    <SelectItem value="airtel_money">Airtel Money</SelectItem>
                    <SelectItem value="zamtel_kwacha">Zamtel Kwacha</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="test-description">Description (Optional)</Label>
                <Input
                  id="test-description"
                  placeholder="Test payment description"
                  value={testFormData.description}
                  onChange={(e) => handleTestFormChange('description', e.target.value)}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="test-reference">Reference (Optional)</Label>
                  <Input
                    id="test-reference"
                    placeholder="AUTO-GENERATED"
                    value={testFormData.reference}
                    onChange={(e) => handleTestFormChange('reference', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="test-callback">Callback URL (Optional)</Label>
                  <Input
                    id="test-callback"
                    placeholder="https://your-site.com/callback"
                    value={testFormData.callback_url}
                    onChange={(e) => handleTestFormChange('callback_url', e.target.value)}
                  />
                </div>
              </div>

              {/* Test Result Display */}
              {testResult.status && (
                <div className={`p-4 rounded-lg border ${
                  testResult.status === 'success'
                    ? 'bg-green-50 border-green-200'
                    : testResult.status === 'error'
                    ? 'bg-red-50 border-red-200'
                    : 'bg-yellow-50 border-yellow-200'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    {testResult.status === 'success' && <CheckCircle className="h-5 w-5 text-green-600" />}
                    {testResult.status === 'error' && <XCircle className="h-5 w-5 text-red-600" />}
                    {testResult.status === 'pending' && <Clock className="h-5 w-5 text-yellow-600" />}
                    <span className={`font-medium ${
                      testResult.status === 'success'
                        ? 'text-green-800'
                        : testResult.status === 'error'
                        ? 'text-red-800'
                        : 'text-yellow-800'
                    }`}>
                      {testResult.status === 'success' ? 'Success' :
                       testResult.status === 'error' ? 'Error' : 'Processing...'}
                    </span>
                  </div>
                  <p className={`text-sm ${
                    testResult.status === 'success'
                      ? 'text-green-700'
                      : testResult.status === 'error'
                      ? 'text-red-700'
                      : 'text-yellow-700'
                  }`}>
                    {testResult.message}
                  </p>
                  {testResult.data && (
                    <div className="mt-2">
                      <details className="text-xs">
                        <summary className="cursor-pointer font-medium">View Response Data</summary>
                        <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                          {JSON.stringify(testResult.data, null, 2)}
                        </pre>
                      </details>
                    </div>
                  )}
                </div>
              )}

              <Separator />

              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsTestApiDialogOpen(false);
                    resetTestForm();
                  }}
                >
                  Cancel
                </Button>
                <div className="flex gap-2">
                  {testResult.status && (
                    <Button
                      variant="ghost"
                      onClick={resetTestForm}
                      size="sm"
                    >
                      Reset
                    </Button>
                  )}
                  <Button
                    onClick={handleTestApi}
                    disabled={isTestingApi || !testFormData.amount || !testFormData.phone || !testFormData.payment_method}
                    className="min-w-[120px]"
                  >
                    {isTestingApi ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Testing...
                      </>
                    ) : (
                      'Test Payment'
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Businesses;
