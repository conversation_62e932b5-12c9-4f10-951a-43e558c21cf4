import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Copy, Check } from 'lucide-react';

const ApiDocs = () => {
  const [copied, setCopied] = React.useState<string | null>(null);

  const copyToClipboard = (text: string, id: string) => {
    navigator.clipboard.writeText(text);
    setCopied(id);
    setTimeout(() => setCopied(null), 2000);
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-4xl mx-auto">
        <div className="mb-12">
          <h1 className="text-4xl font-bold mb-4">API Documentation</h1>
          <p className="text-xl text-gray-600">
            Integrate InfoPay's payment gateway into your applications with our simple and powerful API.
          </p>
        </div>

        <Tabs defaultValue="overview" className="mb-12">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="authentication">Authentication</TabsTrigger>
            <TabsTrigger value="payments">Payments</TabsTrigger>
            <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Getting Started</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  The InfoPay API allows you to accept payments from your customers using various payment methods 
                  available in Malawi. Our API is RESTful and returns responses in JSON format.
                </p>
                <h3 className="text-lg font-semibold mt-4">Base URL</h3>
                <div className="bg-gray-100 p-4 rounded-md flex justify-between items-center">
                  <code className="text-sm">https://api.infopay.mw/v1</code>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => copyToClipboard('https://api.infopay.mw/v1', 'base-url')}
                  >
                    {copied === 'base-url' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </Button>
                </div>
                <h3 className="text-lg font-semibold mt-4">Requirements</h3>
                <p>To use the InfoPay API, you'll need:</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>An InfoPay account</li>
                  <li>API keys (available in your dashboard)</li>
                  <li>Basic knowledge of HTTP requests</li>
                </ul>
                <div className="mt-6">
                  <Button asChild>
                    <Link to="/register">Create an Account</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="authentication" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Authentication</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  All API requests must be authenticated using your API key. You can find your API key in the 
                  InfoPay dashboard under the Business settings.
                </p>
                <h3 className="text-lg font-semibold mt-4">API Key Authentication</h3>
                <p>
                  Include your API key in the request headers:
                </p>
                <div className="bg-gray-100 p-4 rounded-md overflow-x-auto">
                  <pre className="text-sm">
                    {`// Example request with API key
const response = await fetch('https://api.infopay.mw/v1/payments', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'your_api_key_here'
  },
  body: JSON.stringify({
    amount: 1000,
    currency: 'MWK',
    description: 'Payment for Order #123',
    callback_url: 'https://your-website.com/callback'
  })
});`}
                  </pre>
                </div>
                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mt-4">
                  <p className="text-yellow-700">
                    <strong>Important:</strong> Keep your API keys secure and never share them publicly.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="payments" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Payment Endpoints</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold">Create Payment</h3>
                  <p className="text-gray-600 mb-2">Initiate a payment request</p>
                  <div className="bg-gray-100 p-4 rounded-md">
                    <p className="font-mono mb-2">POST /payments</p>
                    <p className="text-sm mb-2">Request Body:</p>
                    <pre className="text-sm overflow-x-auto">
{`{
  "amount": 1000,
  "currency": "MWK",
  "payment_method": "airtel_money",
  "phone": "0991234567",
  "description": "Payment for Order #123",
  "reference": "ORDER123",
  "callback_url": "https://your-website.com/callback"
}`}
                    </pre>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold">Check Payment Status</h3>
                  <p className="text-gray-600 mb-2">Get the status of a payment</p>
                  <div className="bg-gray-100 p-4 rounded-md">
                    <p className="font-mono mb-2">GET /payments/{'{payment_id}'}</p>
                    <p className="text-sm mb-2">Response:</p>
                    <pre className="text-sm overflow-x-auto">
{`{
  "id": "pay_123456789",
  "amount": 1000,
  "currency": "MWK",
  "status": "SUCCESS",
  "payment_method": "airtel_money",
  "phone": "0991234567",
  "description": "Payment for Order #123",
  "reference": "ORDER123",
  "created_at": "2023-05-19T12:30:00Z",
  "completed_at": "2023-05-19T12:32:00Z"
}`}
                    </pre>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="webhooks" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Webhooks</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  Webhooks allow your application to receive real-time updates about payment events. 
                  When a payment status changes, InfoPay will send a POST request to your specified callback URL.
                </p>
                <h3 className="text-lg font-semibold mt-4">Setting Up Webhooks</h3>
                <p>
                  You can set up webhooks in two ways:
                </p>
                <ol className="list-decimal pl-6 space-y-2">
                  <li>Specify a <code>callback_url</code> when creating a payment</li>
                  <li>Configure a default webhook URL in your InfoPay dashboard</li>
                </ol>
                <h3 className="text-lg font-semibold mt-4">Webhook Payload</h3>
                <div className="bg-gray-100 p-4 rounded-md overflow-x-auto">
                  <pre className="text-sm">
{`{
  "event": "payment.success",
  "data": {
    "id": "pay_123456789",
    "amount": 1000,
    "currency": "MWK",
    "status": "SUCCESS",
    "payment_method": "airtel_money",
    "phone": "0991234567",
    "reference": "ORDER123",
    "created_at": "2023-05-19T12:30:00Z",
    "completed_at": "2023-05-19T12:32:00Z"
  }
}`}
                  </pre>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="bg-gray-100 p-6 rounded-lg">
          <h2 className="text-2xl font-bold mb-4">Need Help?</h2>
          <p className="mb-4">
            If you have any questions or need assistance with the API integration, our support team is here to help.
          </p>
          <Button asChild>
            <Link to="/contact">Contact Support</Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ApiDocs;
