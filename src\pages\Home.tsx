import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowRight, CreditCard, BarChart2, Shield, Zap } from 'lucide-react';

const Home = () => {
  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary to-blue-700 text-white py-12 md:py-20">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-10 md:mb-0">
              <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 text-center md:text-left">
                Communication Channel for All Your Ideas
              </h1>
              <p className="text-lg sm:text-xl mb-8 text-blue-100 text-center md:text-left">
                Connect with your customers through multiple channels including Bulk SMS, WhatsApp,
                USSD, and secure payment gateways.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                <Button asChild size="lg" className="bg-white text-primary hover:bg-blue-50 w-full sm:w-auto">
                  <Link to="/register">Get Started</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-blue-800 w-full sm:w-auto">
                  <Link to="/api-docs">View Documentation</Link>
                </Button>
              </div>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <img
                src="https://placehold.co/600x400/1A365D/FFFFFF/png?text=BulkSend+Cloud"
                alt="BulkSend Cloud Dashboard Preview"
                className="rounded-lg shadow-xl max-w-full h-auto"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 md:py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-10 md:mb-16">
            <h2 className="text-2xl sm:text-3xl font-bold mb-3 md:mb-4">Our Communication Solutions</h2>
            <p className="text-base sm:text-xl text-gray-600 max-w-3xl mx-auto px-2">
              BulkSend Cloud offers a comprehensive suite of communication tools to help you connect with your customers.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8 mb-8 md:mb-16">
            <Card className="h-full">
              <CardContent className="pt-6 h-full flex flex-col">
                <div className="rounded-full bg-blue-100 p-3 w-12 h-12 flex items-center justify-center mb-4 mx-auto sm:mx-0">
                  <CreditCard className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-lg sm:text-xl font-bold mb-2 text-center sm:text-left">Easily send powerful bulk SMS campaigns</h3>
                <p className="text-gray-600 text-center sm:text-left">
                  Lorem Ipsum is simply dummy text of the any an printing and typesetting industry. Lorem Ipsum has been the industry's.
                </p>
              </CardContent>
            </Card>

            <Card className="h-full">
              <CardContent className="pt-6 h-full flex flex-col">
                <div className="rounded-full bg-blue-100 p-3 w-12 h-12 flex items-center justify-center mb-4 mx-auto sm:mx-0">
                  <Zap className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-lg sm:text-xl font-bold mb-2 text-center sm:text-left">5-minute SMS API integration</h3>
                <p className="text-gray-600 text-center sm:text-left">
                  Lorem Ipsum is simply dummy text of the any an printing and typesetting industry. Lorem Ipsum has been the industry's.
                </p>
              </CardContent>
            </Card>

            <Card className="h-full">
              <CardContent className="pt-6 h-full flex flex-col">
                <div className="rounded-full bg-blue-100 p-3 w-12 h-12 flex items-center justify-center mb-4 mx-auto sm:mx-0">
                  <Shield className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-lg sm:text-xl font-bold mb-2 text-center sm:text-left">Convey more with SMS attachments</h3>
                <p className="text-gray-600 text-center sm:text-left">
                  Lorem Ipsum is simply dummy text of the any an printing and typesetting industry. Lorem Ipsum has been the industry's.
                </p>
              </CardContent>
            </Card>

            <Card className="h-full">
              <CardContent className="pt-6 h-full flex flex-col">
                <div className="rounded-full bg-blue-100 p-3 w-12 h-12 flex items-center justify-center mb-4 mx-auto sm:mx-0">
                  <BarChart2 className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-lg sm:text-xl font-bold mb-2 text-center sm:text-left">Receive SMS online any issues</h3>
                <p className="text-gray-600 text-center sm:text-left">
                  Lorem Ipsum is simply dummy text of the any an printing and typesetting industry. Lorem Ipsum has been the industry's.
                </p>
              </CardContent>
            </Card>

            <Card className="h-full">
              <CardContent className="pt-6 h-full flex flex-col">
                <div className="rounded-full bg-blue-100 p-3 w-12 h-12 flex items-center justify-center mb-4 mx-auto sm:mx-0">
                  <BarChart2 className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-lg sm:text-xl font-bold mb-2 text-center sm:text-left">Access real-time delivery and click reports</h3>
                <p className="text-gray-600 text-center sm:text-left">
                  Lorem Ipsum is simply dummy text of the any an printing and typesetting industry. Lorem Ipsum has been the industry's.
                </p>
              </CardContent>
            </Card>

            <Card className="h-full">
              <CardContent className="pt-6 h-full flex flex-col">
                <div className="rounded-full bg-blue-100 p-3 w-12 h-12 flex items-center justify-center mb-4 mx-auto sm:mx-0">
                  <Shield className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-lg sm:text-xl font-bold mb-2 text-center sm:text-left">Get enterprise ready to easily</h3>
                <p className="text-gray-600 text-center sm:text-left">
                  Lorem Ipsum is simply dummy text of the any an printing and typesetting industry. Lorem Ipsum has been the industry's.
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8">
            <Card className="h-full">
              <CardContent className="pt-6 h-full flex flex-col">
                <div className="rounded-full bg-blue-100 p-3 w-12 h-12 flex items-center justify-center mb-4 mx-auto sm:mx-0">
                  <CreditCard className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-lg sm:text-xl font-bold mb-2 text-center sm:text-left">Increase engagement with mobile solutions</h3>
                <p className="text-gray-600 text-center sm:text-left">
                  Lorem Ipsum is simply dummy text of the any an printing and typesetting industry. Lorem Ipsum has been the industry's.
                </p>
              </CardContent>
            </Card>

            <Card className="h-full">
              <CardContent className="pt-6 h-full flex flex-col">
                <div className="rounded-full bg-blue-100 p-3 w-12 h-12 flex items-center justify-center mb-4 mx-auto sm:mx-0">
                  <Zap className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-lg sm:text-xl font-bold mb-2 text-center sm:text-left">Missed call numbers & dual VMNs</h3>
                <p className="text-gray-600 text-center sm:text-left">
                  Lorem Ipsum is simply dummy text of the any an printing and typesetting industry. Lorem Ipsum has been the industry's.
                </p>
              </CardContent>
            </Card>

            <Card className="h-full">
              <CardContent className="pt-6 h-full flex flex-col">
                <div className="rounded-full bg-blue-100 p-3 w-12 h-12 flex items-center justify-center mb-4 mx-auto sm:mx-0">
                  <Shield className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-lg sm:text-xl font-bold mb-2 text-center sm:text-left">Connect with your customers via WhatsApp</h3>
                <p className="text-gray-600 text-center sm:text-left">
                  Lorem Ipsum is simply dummy text of the any an printing and typesetting industry. Lorem Ipsum has been the industry's.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gray-100 py-12 md:py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-2xl sm:text-3xl font-bold mb-3 md:mb-4">Ready to Transform Your Customer Communication?</h2>
          <p className="text-base sm:text-xl text-gray-600 max-w-3xl mx-auto mb-6 md:mb-8 px-2">
            Join hundreds of businesses that trust BulkSend Cloud for their communication needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="w-full sm:w-auto">
              <Link to="/register">Create Your Account <ArrowRight className="ml-2 h-4 w-4" /></Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="w-full sm:w-auto">
              <Link to="/contact">Contact Sales</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
