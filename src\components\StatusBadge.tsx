
import React from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

interface StatusBadgeProps {
  status: 'PENDING' | 'SUCCESS' | 'FAILED' | string;
  className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className }) => {
  console.log('StatusBadge rendering with status:', status);

  const statusColors = {
    PENDING: 'bg-warning/20 text-warning border-warning/20 hover:bg-warning/20',
    SUCCESS: 'bg-success/20 text-success border-success/20 hover:bg-success/20',
    FAILED: 'bg-destructive/20 text-destructive border-destructive/20 hover:bg-destructive/20',
  };

  const statusType = status in statusColors ? status : 'PENDING';
  console.log('Using statusType:', statusType, 'with colors:', statusColors[statusType as keyof typeof statusColors]);

  return (
    <Badge
      variant="outline"
      className={cn(
        "px-2 py-1 rounded-full text-xs font-medium",
        statusColors[statusType as keyof typeof statusColors],
        className
      )}
    >
      {status}
    </Badge>
  );
};

export default StatusBadge;
