
import React from 'react';
import * as dateFns from 'date-fns';
import { Transaction } from '@/services/api';
import StatusBadge from './StatusBadge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from '@/components/ui/button';
import { Eye } from 'lucide-react';

interface TransactionTableProps {
  transactions: Transaction[];
  onViewDetails: (transaction: Transaction) => void;
}

const TransactionTable: React.FC<TransactionTableProps> = ({ transactions, onViewDetails }) => {
  console.log('TransactionTable rendering with', transactions.length, 'transactions');

  return (
    <div className="overflow-x-auto">
      <Table className="border-collapse">
        <TableHeader>
          <TableRow className="bg-gray-50/80 border-b border-gray-200">
            <TableHead className="py-3 text-xs font-semibold uppercase tracking-wider text-gray-600">Reference</TableHead>
            <TableHead className="py-3 text-xs font-semibold uppercase tracking-wider text-gray-600">Date</TableHead>
            <TableHead className="py-3 text-xs font-semibold uppercase tracking-wider text-gray-600">Amount</TableHead>
            <TableHead className="py-3 text-xs font-semibold uppercase tracking-wider text-gray-600">Phone</TableHead>
            <TableHead className="py-3 text-xs font-semibold uppercase tracking-wider text-gray-600">Status</TableHead>
            <TableHead className="py-3 text-xs font-semibold uppercase tracking-wider text-gray-600 text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-12 text-muted-foreground">
                <div className="flex flex-col items-center justify-center space-y-2">
                  <CreditCard className="h-8 w-8 text-gray-300" />
                  <p className="text-gray-500">No transactions found</p>
                </div>
              </TableCell>
            </TableRow>
          ) : (
            transactions.map((transaction) => {
              console.log('Rendering transaction:', transaction.id);
              return (
                <TableRow
                  key={transaction.id}
                  className="hover:bg-gray-50 border-b border-gray-100 transition-colors duration-150"
                >
                  <TableCell className="font-medium text-gray-900 py-4">
                    <div className="truncate max-w-[120px]">
                      {transaction.client_reference || transaction.reference}
                    </div>
                  </TableCell>
                  <TableCell className="text-gray-700 py-4">
                    {(() => {
                      try {
                        return dateFns.format(new Date(transaction.created_at), 'MMM dd, yyyy HH:mm');
                      } catch (error) {
                        console.error('Error formatting date:', error, 'for date:', transaction.created_at);
                        return transaction.created_at;
                      }
                    })()}
                  </TableCell>
                  <TableCell className="font-mono font-semibold text-gray-900 py-4">
                    {(() => {
                      try {
                        return `K${transaction.amount.toFixed(2)}`;
                      } catch (error) {
                        console.error('Error formatting amount:', error, 'for amount:', transaction.amount);
                        return `K${transaction.amount}`;
                      }
                    })()}
                  </TableCell>
                  <TableCell className="text-gray-600 py-4">
                    {transaction.phone}
                  </TableCell>
                  <TableCell className="py-4">
                    <StatusBadge status={transaction.status} />
                  </TableCell>
                  <TableCell className="text-right py-4">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => onViewDetails(transaction)}
                      className="text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50 rounded-full"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              );
            })
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default TransactionTable;
