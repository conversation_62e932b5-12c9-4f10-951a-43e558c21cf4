
import axios from 'axios';
import { toast } from 'sonner';

// Define the base URL for our API
const API_URL = 'http://localhost:3000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for authentication
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');

  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  return config;
});

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    const message = error.response?.data?.message || 'An error occurred';
    toast.error(message);
    return Promise.reject(error);
  }
);

// Types for our API responses
export interface AuthResponse {
  token: string;
  user: User;
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
}

export interface Business {
  id: string;
  name: string;
  description?: string;
  api_key: string;
  api_secret: string;
  created_at: string;
  updated_at: string;
}

export interface Transaction {
  id: string;
  amount: number;
  phone: string;
  payment_method: string;
  description?: string;
  reference: string;
  client_reference?: string;
  status: 'PENDING' | 'SUCCESS' | 'FAILED';
  created_at: string;
  completed_at?: string;
}

export interface StatisticsResponse {
  total_transactions: number;
  successful_transactions: number;
  failed_transactions: number;
  pending_transactions: number;
  total_amount: number;
  daily_amounts: {
    date: string;
    amount: number;
  }[];
}

// Auth service
export const authService = {
  register: (userData: { name: string; email: string; phone: string; password: string }) =>
    api.post<{ status: string; message: string }>('/users/register', userData),

  login: (credentials: { email: string; password: string }) =>
    api.post<{ status: string; message: string; data: AuthResponse }>('/users/login', credentials),

  getProfile: () =>
    api.get<{ status: string; data: User }>('/users/profile'),

  updateProfile: (data: Partial<User>) =>
    api.put<{ status: string; message: string; data: User }>('/users/profile', data),

  changePassword: (data: { current_password: string; new_password: string }) =>
    api.put<{ status: string; message: string }>('/users/password', data),
};

// Business service
export const businessService = {
  getBusinesses: () =>
    api.get<{ status: string; data: Business[] }>('/businesses'),

  createBusiness: (data: { name: string; description?: string }) =>
    api.post<{ status: string; message: string; data: Business }>('/businesses', data),

  updateBusiness: (id: string, data: { name: string; description?: string }) =>
    api.put<{ status: string; message: string; data: Business }>(`/businesses/${id}`, data),

  regenerateKey: (id: string) =>
    api.post<{ status: string; message: string; data: { api_key: string; api_secret: string } }>(`/businesses/${id}/regenerate-key`),

  getTransactions: (id: string, params?: { page?: number; limit?: number; status?: string; from_date?: string; to_date?: string; reference?: string }) =>
    api.get<{ status: string; data: Transaction[]; pagination: { total: number; page: number; limit: number } }>(`/businesses/${id}/transactions`, { params }),

  getStatistics: (id: string, period: 'daily' | 'weekly' | 'monthly') =>
    api.get<{ status: string; data: StatisticsResponse }>(`/businesses/${id}/statistics`, { params: { period } }),
};

// Payment service for client-facing API calls
export const paymentService = {
  processPayment: (
    data: {
      amount: number;
      phone: string;
      payment_method: string;
      description?: string;
      reference?: string;
      callback_url?: string
    },
    apiKey: string
  ) =>
    axios.post<{
      status: string;
      message: string;
      data: {
        transaction_id: string;
        client_reference?: string;
        status: string;
        reference: string
      }
    }>(`${API_URL}/payments`, data, {
      headers: { 'X-API-Key': apiKey }
    }),

  checkStatus: (transactionId: string, apiKey: string) =>
    axios.get<{
      status: string;
      data: {
        transaction_id: string;
        payment_status: string;
        details: {
          amount: number;
          phone: string;
          payment_method: string;
          reference: string;
          completed_at?: string;
        }
      }
    }>(`${API_URL}/payments/${transactionId}`, {
      headers: { 'X-API-Key': apiKey }
    }),
};

export default api;
